"""
Plotly三元图绘制示例脚本
实现Macro、<PERSON><PERSON>、Micro孔隙度三元图，使用渗透率作为颜色映射
"""

import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from typing import Optional, Tuple, Dict, Any


def generate_sample_data(n_points: int = 100) -> pd.DataFrame:
    """生成示例三元图数据

    Args:
        n_points: 数据点数量

    Returns:
        包含Macro、Meso、Micro孔隙度和渗透率的DataFrame
    """
    np.random.seed(42)

    # 生成三个组分，确保和为100%
    macro = np.random.uniform(0, 100, n_points)
    meso = np.random.uniform(0, 100 - macro, n_points)
    micro = 100 - macro - meso

    # 生成渗透率数据（对数正态分布）
    perm = np.random.lognormal(mean=1, sigma=2, size=n_points)

    return pd.DataFrame({
        'Macro': macro,
        'Meso': meso,
        'Micro': micro,
        'Perm': perm
    })


def create_ternary_plot(
    data: pd.DataFrame,
    macro_col: str = 'Macro',
    meso_col: str = 'Meso',
    micro_col: str = 'Micro',
    color_col: str = 'Perm',
    title: str = "Macro, Meso, Micro Porosity Ternary Plot",
    well_name: str = "1-SPS-108_CURACAO-1",
    colorscale: str = 'Viridis',
    size: int = 8,
    width: int = 800,
    height: int = 700
) -> go.Figure:
    """创建三元图

    Args:
        data: 包含三元组分和颜色数据的DataFrame
        macro_col: Macro孔隙度列名
        meso_col: Meso孔隙度列名
        micro_col: Micro孔隙度列名
        color_col: 颜色映射列名
        title: 图表标题
        well_name: 井名
        colorscale: 颜色映射方案
        size: 数据点大小
        width: 图表宽度
        height: 图表高度

    Returns:
        plotly Figure对象
    """

    fig = go.Figure(go.Scatterternary({
        'mode': 'markers',
        'a': data[macro_col],
        'b': data[meso_col],
        'c': data[micro_col],
        'marker': {
            'color': data[color_col],
            'colorscale': colorscale,
            'size': size,
            'colorbar': {
                'title': f'Color: {color_col}',
                'titleside': 'right'
            },
            'line': {'width': 0.5, 'color': 'white'}
        },
        'text': [f'{color_col}: {val:.2f}' for val in data[color_col]],
        'hovertemplate':
            f'<b>{macro_col}</b>: %{{a:.1f}}%<br>' +
            f'<b>{meso_col}</b>: %{{b:.1f}}%<br>' +
            f'<b>{micro_col}</b>: %{{c:.1f}}%<br>' +
            f'<b>{color_col}</b>: %{{marker.color:.2f}}<br>' +
            '<extra></extra>'
    }))

    fig.update_layout({
        'title': {
            'text': f'{title}<br><sub>Well: {well_name}</sub>',
            'x': 0.5,
            'font': {'size': 16}
        },
        'ternary': {
            'sum': 100,
            'aaxis': {
                'title': f'{macro_col} Porosity',
                'min': 0,
                'linewidth': 2,
                'ticks': 'outside'
            },
            'baxis': {
                'title': f'{meso_col} Porosity',
                'min': 0,
                'linewidth': 2,
                'ticks': 'outside'
            },
            'caxis': {
                'title': f'{micro_col} Porosity',
                'min': 0,
                'linewidth': 2,
                'ticks': 'outside'
            }
        },
        'width': width,
        'height': height,
        'font': {'family': 'Arial', 'size': 12}
    })

    return fig


def add_polygon_regions(
    fig: go.Figure,
    regions: Optional[Dict[str, Dict[str, Any]]] = None
) -> go.Figure:
    """添加多边形区域到三元图

    Args:
        fig: plotly Figure对象
        regions: 区域定义字典

    Returns:
        更新后的Figure对象
    """

    if regions is None:
        # 默认区域定义
        regions = {
            'Macro_P': {
                'a': [100, 80, 60, 80],
                'b': [0, 0, 20, 20],
                'c': [0, 20, 20, 0],
                'color': 'yellow',
                'opacity': 0.3
            },
            'Meso_P': {
                'a': [0, 20, 40, 20],
                'b': [100, 80, 60, 80],
                'c': [0, 0, 0, 0],
                'color': 'orange',
                'opacity': 0.3
            },
            'Micro_P': {
                'a': [0, 0, 20, 20],
                'b': [0, 20, 20, 0],
                'c': [100, 80, 60, 80],
                'color': 'purple',
                'opacity': 0.3
            }
        }

    for region_name, region_data in regions.items():
        fig.add_trace(go.Scatterternary({
            'mode': 'lines',
            'a': region_data['a'],
            'b': region_data['b'],
            'c': region_data['c'],
            'fill': 'toself',
            'fillcolor': region_data['color'],
            'opacity': region_data['opacity'],
            'line': {'width': 1, 'color': region_data['color']},
            'name': region_name,
            'showlegend': True,
            'hoverinfo': 'name'
        }))

    return fig


def save_ternary_plot(
    fig: go.Figure,
    filename: str,
    formats: Tuple[str, ...] = ('html', 'png'),
    **kwargs
) -> None:
    """保存三元图到文件

    Args:
        fig: plotly Figure对象
        filename: 文件名（不含扩展名）
        formats: 保存格式元组
        **kwargs: 传递给保存函数的额外参数
    """

    for fmt in formats:
        if fmt == 'html':
            fig.write_html(f"{filename}.html", **kwargs)
            print(f"已保存HTML文件: {filename}.html")
        elif fmt == 'png':
            fig.write_image(f"{filename}.png", **kwargs)
            print(f"已保存PNG文件: {filename}.png")
        elif fmt == 'pdf':
            fig.write_image(f"{filename}.pdf", **kwargs)
            print(f"已保存PDF文件: {filename}.pdf")
        elif fmt == 'svg':
            fig.write_image(f"{filename}.svg", **kwargs)
            print(f"已保存SVG文件: {filename}.svg")


def main():
    """主函数：演示三元图绘制"""

    # 生成示例数据
    print("生成示例数据...")
    data = generate_sample_data(n_points=150)

    # 创建基础三元图
    print("创建三元图...")
    fig = create_ternary_plot(
        data=data,
        macro_col='Macro',
        meso_col='Meso',
        micro_col='Micro',
        color_col='Perm',
        title="Macro, Meso, Micro Porosity Ternary Plot",
        well_name="1-SPS-108_CURACAO-1",
        colorscale='Plasma'
    )

    # 添加多边形区域
    print("添加多边形区域...")
    fig = add_polygon_regions(fig)

    # 显示图表
    print("显示图表...")
    fig.show()

    # 保存图表
    print("保存图表...")
    save_ternary_plot(
        fig,
        filename="ternary_plot_example",
        formats=('html', 'png'),
        width=800,
        height=700
    )

    print("三元图绘制完成！")


if __name__ == "__main__":
    main()
